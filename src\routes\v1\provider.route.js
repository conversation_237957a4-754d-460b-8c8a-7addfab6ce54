const express = require('express');
const validate = require('../../middlewares/validate');
const { provider<PERSON>ontroller, questionController, productController , orderController , reviewController, notifyController} = require('../../controllers');
const { providerValidation, productValidation , orderValidation} = require('../../validations');
const auth = require('../../middlewares/auth');
const fileUpload = require('../../middlewares/fileUpload');
const { createModelQueryFilter } = require('../../middlewares/queryFilter');

const router = express.Router();

// ############## Profile Routes ##############
router.post('/profile', auth('providerRootAccess'), fileUpload('uploads/providers', [
    { fieldName: 'photoId', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, },
    { fieldName: 'cannabisLicense', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, },
    { fieldName: 'resellersPermit', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, },
    { fieldName: 'image', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf', ], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false, },
  ]), (req, res, next) => { req.body.user = req.user._id; next(); } , providerController.updateProvider);

router.get('/profile', auth('providerRootAccess'), providerController.getProfile);

// ############## Dashboard Route ##############
router.get('/dashboard', auth('providerRootAccess'), providerController.getDashboard);

  // ############## Product Routes ##############
router.get('/product', auth('providerRootAccess'), createModelQueryFilter('Product'), productController.index);
router.get('/product/:product', auth('providerRootAccess'), validate(productValidation.params), productController.view);
router.post('/product', auth('providerRootAccess'),
  fileUpload('uploads/products', [{ fieldName: 'photo', allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: true, multiple: true }]),
  validate(productValidation.create), productController.create);
router.put('/product', auth('providerRootAccess'),
  fileUpload('uploads/products', [{ fieldName: 'photo', allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: true }]),
  validate(productValidation.update), productController.update);
router.delete('/product', auth('providerRootAccess'), validate(productValidation.body), productController.softDelete);

// ################# Order Routes #################
router.get('/order/:order', auth('providerRootAccess'),  orderController.index);
router.patch('/order/status', auth('providerRootAccess'),validate(orderValidation.update), orderController.updateStatus);

//################ Review Routes ################
router.get('/reviews/:providerId', auth('providerRootAccess'), createModelQueryFilter('Review'), reviewController.getReviews);
router.get('/reviews', auth('providerRootAccess'), createModelQueryFilter('Review'), reviewController.getReviews); // Get all reviews with query filters

// ################## Notification Routes ##################
router.post('/save-fcm-token',auth('providerRootAccess'), notifyController.saveFcmToken);

// ########## Provider Update Route (Admin/Manager) ##########
// Support both /:id and / (with provider ID in body)
router.put(
  '/:id?',
  auth('providerRootAccess'), // Adjust permission as needed
  fileUpload('uploads/providers', [
    { fieldName: 'photoId', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false },
    { fieldName: 'cannabisLicense', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false },
    { fieldName: 'resellersPermit', allowedTypes: [ 'image/jpeg', 'image/jpg', 'image/png', 'application/pdf'], minSize: 20 * 1024, maxSize: 2000 * 1024, fileRequired: false, multiple: false },
  ]),
  validate(providerValidation.update),
  providerController.update
);

module.exports = router;