const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');
const { required } = require('joi');

const providerSchema = mongoose.Schema(
  {

    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true,
    },
    image: {
      type: String,
      required: false,
    },
    photoId: {
      type: String,
      required: true,
    },
    cannabisLicense: {
      type: String,
      required: true,
    },
    resellersPermit: {
      type: String,
      required: true,
    },
    street: {
      type: String,
      required: true,
    },
    city: {
      type: String,
      required: true,
    },
    state: {
      type: String,
      required: true,
    },
    country: {
      type: String,
      required: true,
    },
    zipCode: {
      type: Number,
      required: true,
    },
    location: {
      type: {
        type: String,
        enum: ['Point'],
        required: true,
        default: 'Point',
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        required: true,
        validate: {
          validator: function (v) {
            return v.length === 2 && 
                 typeof v[0] === 'number' && // longitude
                 typeof v[1] === 'number';   // latitude
          },
          message: 'Coordinates must be an array of two numbers: [longitude, latitude]',
        },
      },
    },
    radius: {
      type: Number,
      required: true,
    },
    paymentOption: {
      type: [String],
      required: true,
      enum: ['Offline', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking'], // Restrict to valid payment options
    },    
    businessType: {
      type: [String],
      required: true,
      enum: ['Offline', 'Online'], 
    },    
    deleviryType: {
      type: [String],
      required: true,
      enum: ['Offline', 'Online'], 
    },
    startTime: {
      type: String,
      required: true,
      match: /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, // Matches HH:mm format (24-hour clock)
    },
    endTime: {
      type: String,
      required: true,
      match: /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, // Matches HH:mm format (24-hour clock)
    },
    availableDays: {
      type: [String], // Array of weekdays
      required: true,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'], // Restrict to valid weekdays
    },
    isApproved: {
      type: String,
      enum: ['pending', 'rejected', 'approved'],
      required: true,
      default: 'pending', // Default to 'pending' until reviewed
    },
    rating:{
      type: Number,
      required: false,
    },
    phone: {
      type: String,
      required:false
    }
  },
  {
    timestamps: true,
  }
);

providerSchema.index({ location: '2dsphere' });
providerSchema.index({ user: 1 }); // Optimize auth middleware queries
// Add plugins for JSON conversion and pagination
providerSchema.plugin(toJSON);
providerSchema.plugin(paginate);
providerSchema.plugin(MongooseDelete, { overrideMethods: 'all' });

/**
 * @typedef Provider
 */
const Provider = mongoose.model('Provider', providerSchema);
module.exports = Provider;
