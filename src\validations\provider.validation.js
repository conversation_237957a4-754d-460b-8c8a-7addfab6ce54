const Joi = require('joi');
const { exists, unique, objectId, password, latitude, longitude, timeComparison } = require('./custom.validation');
const { Provider, User } = require('../models');
const { getByIdWithoutAggregation } = require('../services/GlobalService');

const provider = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Provider, field: '_id', value });
    return value;
  });

const query = {
  query: Joi.object().keys({
    provider,
  }),
};

const params = {
  params: Joi.object().keys({
    provider,
  }),
};

const create = {
  body: Joi.object().keys({
    email: Joi.string()
      .required()
      .email()
      .external(async (value) => {
        await unique({ model: User, field: 'email', value });
        return value;
      }),
    password: Joi.string().required().custom(password),
    name: Joi.string().required(),
    description: Joi.string().required(),
    image: Joi.string().uri().optional().allow(null),
    photoId: Joi.string().uri().required(),
    cannabisLicense: Joi.string().uri().required(),
    resellersPermit: Joi.string().uri().required(),
    street: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    zipCode: Joi.number().integer().required(),
    latitude: Joi.custom(latitude).required(),
    longitude: Joi.custom(longitude).required(),
    radius: Joi.string().required(),
    paymentOption: Joi.array()
      .items(Joi.string().valid('Offline', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking'))
      .required(),
    startTime: Joi.string()
      .required()
      .pattern(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/), // Validate HH:mm format
    endTime: Joi.string()
      .required()
      .pattern(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/) // Validate HH:mm format
      .custom((value, helpers) =>
        timeComparison(value, helpers, helpers.state.ancestors[0].startTime, 'greaterThanOrEqualTo', 'HH:mm')
      ), // Ensuring endTime is greater than or equal to startTime with custom format
    availableDays: Joi.array()
      .items(Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'))
      .required(),
    isApproved: Joi.string().valid('pending', 'rejected', 'approved'),
    rating: Joi.number().min(0).max(5).optional(),
  }),
};

const update = {
  params: Joi.object().keys({
    id: Joi.string().optional().custom(objectId).external(async (value) => {
      if (value) {
        await exists({ model: Provider, field: '_id', value });
      }
      return value;
    }),
  }),
  body: Joi.object({
    provider: Joi.string().optional().custom(objectId).external(async (value) => {
      if (value) {
        await exists({ model: Provider, field: '_id', value });
      }
      return value;
    }), // Accept provider ID from body
    email: Joi.string()
      .email()
      .external(async (value, helpers) => {
        if (!value) return value;

        // Get provider ID from either URL params or request body
        // The validation context structure is: { params: {...}, body: {...} }
        const rootObject = helpers.state.ancestors[1]; // Root validation object
        const paramsId = rootObject?.params?.id;
        const bodyProvider = helpers.state.ancestors[0]?.provider; // Current body object
        const providerId = paramsId || bodyProvider;

        let providerData = providerId ? await getByIdWithoutAggregation("Provider", providerId) : null;
        await unique({ model: User, field: 'email', value, excludeId: providerData ? providerData.user : undefined });
        return value;
      }),
    name: Joi.string(),
    description: Joi.string(),
    image: Joi.string().uri(),
    photoId: Joi.string().uri(),
    cannabisLicense: Joi.string().uri(),
    resellersPermit: Joi.string().uri(),
    street: Joi.string(),
    city: Joi.string(),
    state: Joi.string(),
    country: Joi.string(),
    zipCode: Joi.number().integer(),
    latitude: Joi.custom(latitude),
    longitude: Joi.custom(longitude),
    radius: Joi.number(),
    paymentOption: Joi.array().items(Joi.string().valid('Offline', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking')),
    startTime: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/),
    endTime: Joi.string()
      .pattern(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/)
      .custom((value, helpers) => {
        if (!value) return value;
        return timeComparison(value, helpers, helpers.state.ancestors[0].startTime, 'greaterThanOrEqualTo', 'HH:mm');
      }),
    availableDays: Joi.array().items(Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')),
    isApproved: Joi.string().valid('pending', 'rejected', 'approved'),
  }),
};

const status = {
  body: Joi.object().keys({
    provider,
    isApproved: Joi.string().valid('pending', 'rejected', 'approved'),
  }),
};

const profile = {
  body: Joi.object({
    user: Joi.required(),
    email: Joi.string()
      .optional()
      .email()
      .external(async (value, helpers) => {
        const { user } = helpers.state.ancestors[0];
        await unique({ model: User, field: 'email', value, excludeId: user });
        return value;
      }),
    name: Joi.string().required(),
    description: Joi.string().required(),
    image: Joi.string().uri().optional().allow(null),
    photoId: Joi.string().uri().required().messages({
      'string.empty': '"Photo ID" cannot be empty',
      'string.uri': '"Photo ID" is required',
      'any.required': '"Photo ID" is required',
    }),
    cannabisLicense: Joi.string().uri().required().messages({
      'string.empty': '"Cannabis License" cannot be empty',
      'string.uri': '"Cannabis License" is required',
      'any.required': '"Cannabis License" is required',
    }),
    resellersPermit: Joi.string().uri().required().messages({
      'string.empty': '"Resellers Permit" cannot be empty',
      'string.uri': '"Resellers Permit" is required',
      'any.required': '"Resellers Permit" is required',
    }),
    street: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    country: Joi.string().required(),
    zipCode: Joi.number().integer().required(),
    latitude: Joi.custom(latitude).required(),
    longitude: Joi.custom(longitude).required(),
    radius: Joi.string().required(),
    paymentOption: Joi.array()
      .items(Joi.string().valid('Offline', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking'))
      .required(),
    businessType: Joi.array()
      .items(Joi.string().valid('Offline', 'Online'))
      .required(),
    deleviryType: Joi.array()
      .items(Joi.string().valid('Offline', 'Online'))
      .required(),
    startTime: Joi.string()
      .required()
      .pattern(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/), // Validate HH:mm format
    endTime: Joi.string()
      .required()
      .pattern(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/) // Validate HH:mm format
      .custom((value, helpers) =>
        timeComparison(value, helpers, helpers.state.ancestors[0].startTime, 'greaterThanOrEqualTo', 'HH:mm')
      ),
    availableDays: Joi.array()
      .items(Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'))
      .required(),
  }),
};

module.exports = {
  create,
  params,
  query,
  update,
  status,
  profile,
};
