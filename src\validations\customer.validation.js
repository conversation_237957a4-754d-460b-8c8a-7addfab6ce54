const Joi = require('joi');
const { exists, unique, objectId, password, latitude, longitude } = require('./custom.validation');
const { Customer, User } = require('../models');
const { getByIdWithoutAggregation } = require('../services/GlobalService');
const customer = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Customer, field: '_id', value });
    return value;
  });

const query = {
  query: Joi.object().keys({
    customer,
  }),
};

const params = {
  params: Joi.object().keys({
    customer,
  }),
};

const create = {
  body: Joi.object().keys({
    email: Joi.string()
      .required()
      .email()
      .external(async (value) => {
        await unique({ model: User, field: 'email', value });
        return value;
      }),
    password: Joi.string().required().custom(password),
    username: Joi.string()
      .required()
      .external(async (value) => {
        await unique({ model: Customer, field: 'username', value });
        return value;
      }),
    emailPermission: Joi.boolean().optional(),
    photo: Joi.string().uri().optional().allow(null),
    bio: Joi.string().max(500).optional(),
    notificationPermission: Joi.boolean().optional(),
    latitude: Joi.custom(latitude).optional(),
    longitude: Joi.custom(longitude).optional(),
    address: Joi.object({
      pincode: Joi.string().allow(null),
      locality: Joi.string().allow(null),
      address_line: Joi.string().allow(null),
      city: Joi.string().allow(null),
      state: Joi.string().allow(null),
      landmark: Joi.string().allow(null)
    }).allow(null),
  }),
};

const update = {
  body: Joi.object({
    customer,
    email: Joi.string()
      .required()
      .email()
      .external(async (value, helpers) => {
        const { customer } = helpers.state.ancestors[0];
        let customerData = await getByIdWithoutAggregation('Customer', customer);
        await unique({ model: User, field: 'email', value, excludeId: customerData._doc.user });
        return value;
      }),
    username: Joi.string()
      .required()
      .external(async (value, helpers) => {
        const { customer } = helpers.state.ancestors[0];
        await unique({ model: Customer, field: 'username', value, excludeId: customer });
        return value;
      }),
    emailPermission: Joi.boolean().required(),
    photo: Joi.string().uri().optional().allow(null),
    bio: Joi.string().max(500).optional(),
    notificationPermission: Joi.boolean().optional(),
    latitude: Joi.custom(latitude).optional(),
    longitude: Joi.custom(longitude).optional(),
    address: Joi.object({
      pincode: Joi.string().allow(null),
      locality: Joi.string().allow(null),
      address_line: Joi.string().allow(null),
      city: Joi.string().allow(null),
      state: Joi.string().allow(null),
      landmark: Joi.string().allow(null)
    }).allow(null),
  }),
};

const updateProfile = {
  body: Joi.object({
    user: Joi.required(),
    email: Joi.string()
      .required()
      .email()
      .external(async (value, helpers) => {
        const { user } = helpers.state.ancestors[0];
        await unique({ model: User, field: 'email', value, excludeId: user });
        return value;
      }),
    username: Joi.string()
      .required()
      .external(async (value, helpers) => {
        const { customer } = helpers.state.ancestors[0];
        await unique({ model: Customer, field: 'username', value, excludeId: customer });
        return value;
      }),
    emailPermission: Joi.boolean().required(),
    photo: Joi.string().uri().optional().allow(null),
    bio: Joi.string().max(500).optional(),
    notificationPermission: Joi.boolean().optional(),
    location: Joi.object({
      type: Joi.string().valid('Point').optional(),
      coordinates: Joi.array()
        .items(
          Joi.number().min(-180).max(180).optional(), // Longitude
          Joi.number().min(-90).max(90).optional() // Latitude
        )
        .length(2)
        .optional(),
    }).optional(),
    address: Joi.string().optional(),
  }),
};

const username = {
  body: Joi.object().keys({
    username: Joi.string()
      .required()
      .external(async (value) => {
        await unique({ model: Customer, field: 'username', value });
        return value;
      }),
    emailPermission: Joi.boolean().optional(),
  }),
};

const profile = {
  body: Joi.object().keys({
    photo: Joi.alternatives().try(
      Joi.string().uri(), // Accept URL string
      Joi.string().allow('') // Accept file upload (will be processed by middleware)
    ).optional().allow(null),
    bio: Joi.string().max(500).optional().allow(''),
  }),
};

const address = {
  params: Joi.object().keys({
    location: Joi.object({
      type: Joi.string().valid('Point').optional(),
      coordinates: Joi.array()
        .items(
          Joi.number().min(-180).max(180), // Longitude
          Joi.number().min(-90).max(90) // Latitude
        )
        .length(2)
        .optional(),
    }).optional(),
    address: Joi.string().optional(),
  }),
};
const customerAddress = {
  body: Joi.object().keys({
    location: Joi.object({
      type: Joi.string().valid('Point').required(),
      coordinates: Joi.array()
        .items(
          Joi.number().min(-180).max(180), // Longitude
          Joi.number().min(-90).max(90) // Latitude
        )
        .length(2)
        .required(),
    }).required(),
  }),
};

const notification = {
  params: Joi.object().keys({
    notificationPermission: Joi.boolean().optional(),
  }),
};

module.exports = {
  create,
  params,
  query,
  update,
  updateProfile,
  username,
  profile,
  address,
  notification,
  customerAddress,
};
